<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AI Chat Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
    </style>
</head>
<body>
    <h1>AI Chat Application - Functionality Test</h1>
    
    <div class="test-container">
        <h2>1. Test AI Persona Button</h2>
        <button class="test-button" onclick="testPersonaButton()">Test Persona Button</button>
        <div id="persona-result" class="test-result" style="display: none;"></div>
    </div>
    
    <div class="test-container">
        <h2>2. Test Export/Import Button</h2>
        <button class="test-button" onclick="testExportButton()">Test Export Button</button>
        <div id="export-result" class="test-result" style="display: none;"></div>
    </div>
    
    <div class="test-container">
        <h2>3. Test Chat Naming</h2>
        <button class="test-button" onclick="testChatNaming()">Test Chat Title Generation</button>
        <div id="naming-result" class="test-result" style="display: none;"></div>
    </div>
    
    <div class="test-container">
        <h2>4. Test Feature Managers</h2>
        <button class="test-button" onclick="testFeatureManagers()">Test Manager Initialization</button>
        <div id="managers-result" class="test-result" style="display: none;"></div>
    </div>

    <script>
        function testPersonaButton() {
            const result = document.getElementById('persona-result');
            result.style.display = 'block';
            
            const button = document.getElementById('ai-persona-toggle');
            const panel = document.getElementById('ai-persona-panel');
            
            if (!button) {
                result.innerHTML = '❌ AI Persona button not found';
                result.className = 'test-result error';
                return;
            }
            
            if (!panel) {
                result.innerHTML = '❌ AI Persona panel not found';
                result.className = 'test-result error';
                return;
            }
            
            // Test button click
            try {
                button.click();
                setTimeout(() => {
                    const isVisible = panel.style.display !== 'none' || panel.classList.contains('open');
                    if (isVisible) {
                        result.innerHTML = '✅ AI Persona button works! Panel opened successfully.';
                        result.className = 'test-result success';
                    } else {
                        result.innerHTML = '❌ AI Persona button clicked but panel did not open';
                        result.className = 'test-result error';
                    }
                }, 100);
            } catch (error) {
                result.innerHTML = `❌ Error testing persona button: ${error.message}`;
                result.className = 'test-result error';
            }
        }
        
        function testExportButton() {
            const result = document.getElementById('export-result');
            result.style.display = 'block';
            
            const button = document.getElementById('export-import-toggle');
            const panel = document.getElementById('export-import-panel');
            
            if (!button) {
                result.innerHTML = '❌ Export/Import button not found';
                result.className = 'test-result error';
                return;
            }
            
            if (!panel) {
                result.innerHTML = '❌ Export/Import panel not found';
                result.className = 'test-result error';
                return;
            }
            
            // Test button click
            try {
                button.click();
                setTimeout(() => {
                    const isVisible = panel.style.display !== 'none' || panel.classList.contains('open');
                    if (isVisible) {
                        result.innerHTML = '✅ Export/Import button works! Panel opened successfully.';
                        result.className = 'test-result success';
                    } else {
                        result.innerHTML = '❌ Export/Import button clicked but panel did not open';
                        result.className = 'test-result error';
                    }
                }, 100);
            } catch (error) {
                result.innerHTML = `❌ Error testing export button: ${error.message}`;
                result.className = 'test-result error';
            }
        }
        
        function testChatNaming() {
            const result = document.getElementById('naming-result');
            result.style.display = 'block';
            
            // Test the generateChatTitle function if available
            if (typeof generateChatTitle === 'function') {
                const testHistory1 = [
                    { role: 'user', content: 'How do I create a React component?' },
                    { role: 'assistant', content: 'Here is how you create a React component...' }
                ];
                
                const testHistory2 = [
                    { role: 'system', content: 'You are an expert software engineer...' },
                    { role: 'user', content: 'Help me debug this JavaScript function that calculates fibonacci numbers' },
                    { role: 'assistant', content: 'I can help you debug that...' }
                ];
                
                const title1 = generateChatTitle(testHistory1);
                const title2 = generateChatTitle(testHistory2);
                
                result.innerHTML = `✅ Chat naming works!<br>
                    Test 1: "${title1}"<br>
                    Test 2: "${title2}"`;
                result.className = 'test-result success';
            } else {
                result.innerHTML = '❌ generateChatTitle function not found';
                result.className = 'test-result error';
            }
        }
        
        function testFeatureManagers() {
            const result = document.getElementById('managers-result');
            result.style.display = 'block';
            
            let status = [];
            
            // Check AI Persona Manager
            if (window.aiPersonaManager) {
                status.push('✅ AI Persona Manager: Initialized');
            } else {
                status.push('❌ AI Persona Manager: Not found');
            }
            
            // Check Export/Import Manager
            if (window.exportImportManager) {
                status.push('✅ Export/Import Manager: Initialized');
            } else {
                status.push('❌ Export/Import Manager: Not found');
            }
            
            // Check Code Integration Manager
            if (window.codeIntegrationManager) {
                status.push('✅ Code Integration Manager: Initialized');
            } else {
                status.push('❌ Code Integration Manager: Not found');
            }
            
            const allGood = status.every(s => s.includes('✅'));
            result.innerHTML = status.join('<br>');
            result.className = allGood ? 'test-result success' : 'test-result error';
        }
        
        // Auto-run tests when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                console.log('Running automatic functionality tests...');
                testFeatureManagers();
            }, 1000);
        });
    </script>
</body>
</html>
