<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AI Persona <PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>AI Persona Button Test</h1>
    
    <div class="test-container">
        <h2>Instructions</h2>
        <p>1. The main application should load below</p>
        <p>2. Look for the AI Persona button (gear icon) in the header</p>
        <p>3. Click it to test if the panel opens</p>
        <p>4. Check the browser console for debug messages</p>
        <button class="test-button" onclick="openConsole()">Open Browser Console</button>
        <button class="test-button" onclick="testInParent()">Test in Parent Window</button>
    </div>
    
    <div class="test-container">
        <h2>Application</h2>
        <iframe src="index.html" id="app-frame"></iframe>
    </div>

    <script>
        function openConsole() {
            alert('Press F12 or right-click and select "Inspect Element" to open the browser console');
        }
        
        function testInParent() {
            // Open the main app in the parent window for easier testing
            window.open('index.html', '_blank');
        }
        
        // Listen for messages from the iframe
        window.addEventListener('message', function(event) {
            console.log('Message from iframe:', event.data);
        });
        
        // Auto-focus the iframe after load
        document.getElementById('app-frame').onload = function() {
            console.log('Application loaded in iframe');
        };
    </script>
</body>
</html>
