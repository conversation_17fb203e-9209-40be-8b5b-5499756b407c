// Debug test script for AI Chat application
// Run this in the browser console to test functionality

console.log('🔍 Starting AI Chat Debug Tests...');

// Test 1: Check if DOM elements exist
function testDOMElements() {
    console.log('\n📋 Test 1: Checking DOM Elements');
    
    const elements = {
        'AI Persona Button': document.getElementById('ai-persona-toggle'),
        'AI Persona Panel': document.getElementById('ai-persona-panel'),
        'Export/Import Button': document.getElementById('export-import-toggle'),
        'Export/Import Panel': document.getElementById('export-import-panel'),
        'Persona Select': document.getElementById('persona-select'),
        'Export Selected Button': document.getElementById('export-selected'),
        'Export All Button': document.getElementById('export-all'),
        'Import Chats Button': document.getElementById('import-chats')
    };
    
    Object.entries(elements).forEach(([name, element]) => {
        if (element) {
            console.log(`✅ ${name}: Found`);
        } else {
            console.log(`❌ ${name}: Not found`);
        }
    });
}

// Test 2: Check if managers are initialized
function testManagers() {
    console.log('\n🏗️ Test 2: Checking Manager Initialization');
    
    const managers = {
        'AI Persona Manager': window.aiPersonaManager,
        'Export/Import Manager': window.exportImportManager,
        'Code Integration Manager': window.codeIntegrationManager
    };
    
    Object.entries(managers).forEach(([name, manager]) => {
        if (manager) {
            console.log(`✅ ${name}: Initialized`);
        } else {
            console.log(`❌ ${name}: Not initialized`);
        }
    });
}

// Test 3: Test chat title generation
function testChatTitleGeneration() {
    console.log('\n📝 Test 3: Testing Chat Title Generation');
    
    if (typeof window.generateChatTitle === 'function') {
        const testCases = [
            {
                name: 'Simple user message',
                history: [
                    { role: 'user', content: 'How do I create a React component?' }
                ]
            },
            {
                name: 'With system message',
                history: [
                    { role: 'system', content: 'You are an expert software engineer...' },
                    { role: 'user', content: 'Help me debug this JavaScript function' }
                ]
            },
            {
                name: 'Long message',
                history: [
                    { role: 'user', content: 'I need help with creating a comprehensive web application that includes user authentication, database integration, real-time updates, and responsive design' }
                ]
            },
            {
                name: 'Generic greeting',
                history: [
                    { role: 'user', content: 'Hi' }
                ]
            },
            {
                name: 'Empty history',
                history: []
            }
        ];
        
        testCases.forEach(testCase => {
            const title = window.generateChatTitle(testCase.history);
            console.log(`✅ ${testCase.name}: "${title}"`);
        });
    } else {
        console.log('❌ generateChatTitle function not found');
    }
}

// Test 4: Test button functionality
function testButtonFunctionality() {
    console.log('\n🔘 Test 4: Testing Button Functionality');
    
    // Test AI Persona button
    const personaButton = document.getElementById('ai-persona-toggle');
    const personaPanel = document.getElementById('ai-persona-panel');
    
    if (personaButton && personaPanel) {
        console.log('Testing AI Persona button...');
        const initialDisplay = personaPanel.style.display;
        const initialClass = personaPanel.classList.contains('open');
        
        // Simulate click
        personaButton.click();
        
        setTimeout(() => {
            const newDisplay = personaPanel.style.display;
            const newClass = personaPanel.classList.contains('open');
            
            if (newDisplay !== initialDisplay || newClass !== initialClass) {
                console.log('✅ AI Persona button: Working (panel state changed)');
            } else {
                console.log('❌ AI Persona button: Not working (panel state unchanged)');
            }
        }, 100);
    } else {
        console.log('❌ AI Persona button: Elements not found');
    }
    
    // Test Export/Import button
    const exportButton = document.getElementById('export-import-toggle');
    const exportPanel = document.getElementById('export-import-panel');
    
    if (exportButton && exportPanel) {
        console.log('Testing Export/Import button...');
        const initialDisplay = exportPanel.style.display;
        const initialClass = exportPanel.classList.contains('open');
        
        // Simulate click
        setTimeout(() => {
            exportButton.click();
            
            setTimeout(() => {
                const newDisplay = exportPanel.style.display;
                const newClass = exportPanel.classList.contains('open');
                
                if (newDisplay !== initialDisplay || newClass !== initialClass) {
                    console.log('✅ Export/Import button: Working (panel state changed)');
                } else {
                    console.log('❌ Export/Import button: Not working (panel state unchanged)');
                }
            }, 100);
        }, 200);
    } else {
        console.log('❌ Export/Import button: Elements not found');
    }
}

// Test 5: Test localStorage functionality
function testLocalStorage() {
    console.log('\n💾 Test 5: Testing LocalStorage Functionality');
    
    try {
        // Test chat history storage
        const testChat = {
            id: 'test-' + Date.now(),
            title: 'Test Chat',
            history: [
                { role: 'user', content: 'Test message' },
                { role: 'assistant', content: 'Test response' }
            ],
            model: 'test-model',
            timestamp: Date.now()
        };
        
        const existingChats = JSON.parse(localStorage.getItem('ollama-chats') || '{}');
        existingChats[testChat.id] = testChat;
        localStorage.setItem('ollama-chats', JSON.stringify(existingChats));
        
        const retrieved = JSON.parse(localStorage.getItem('ollama-chats'));
        if (retrieved[testChat.id]) {
            console.log('✅ Chat history storage: Working');
            // Clean up
            delete retrieved[testChat.id];
            localStorage.setItem('ollama-chats', JSON.stringify(retrieved));
        } else {
            console.log('❌ Chat history storage: Failed');
        }
        
        // Test persona settings storage
        const testPersonaSettings = {
            currentPersona: 'test-persona',
            customPrompt: 'Test prompt',
            expertiseAreas: ['test'],
            responseStyle: 'test'
        };
        
        localStorage.setItem('ai-persona-settings', JSON.stringify(testPersonaSettings));
        const retrievedPersona = JSON.parse(localStorage.getItem('ai-persona-settings'));
        
        if (retrievedPersona && retrievedPersona.currentPersona === 'test-persona') {
            console.log('✅ Persona settings storage: Working');
            // Clean up
            localStorage.removeItem('ai-persona-settings');
        } else {
            console.log('❌ Persona settings storage: Failed');
        }
        
    } catch (error) {
        console.log('❌ LocalStorage test failed:', error.message);
    }
}

// Run all tests
function runAllTests() {
    testDOMElements();
    testManagers();
    testChatTitleGeneration();
    testButtonFunctionality();
    testLocalStorage();
    
    console.log('\n🎯 Debug tests completed! Check the results above.');
}

// Auto-run tests if this script is loaded
if (typeof window !== 'undefined') {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runAllTests, 1000);
        });
    } else {
        setTimeout(runAllTests, 1000);
    }
}

// Export functions for manual testing
window.debugTests = {
    runAllTests,
    testDOMElements,
    testManagers,
    testChatTitleGeneration,
    testButtonFunctionality,
    testLocalStorage
};

console.log('🔧 Debug test functions available at window.debugTests');
